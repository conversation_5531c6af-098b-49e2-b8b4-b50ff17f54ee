<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>生产计划 - 药房库存管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATIC__/ext/layui/css/layui.css">
    <link rel="stylesheet" href="__STATIC__/shop/css/pharmacy.css">
    <style>
        .production-container {
            min-height: 95vh;
            background-color: #f5f5f5;
            padding: 20px;
        }
        .production-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .production-title {
            font-size: 24px;
            color: #333;
            margin: 0;
        }
        .header-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .back-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .back-btn:hover {
            background: #0078D4;
            color: white;
            text-decoration: none;
        }
        .production-main {
            display: flex;
            gap: 20px;
            height: calc(100vh - 140px);
        }
        .production-form {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: calc(100vh - 180px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .production-plan {
            width: 400px;
            background: white;
            border-radius: 8px;
            padding: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: calc(100vh - 180px);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 3px;
            font-weight: bold;
            color: #333;
            font-size: 13px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-shrink: 0;
        }
        .form-group-half {
            flex: 1;
            margin-bottom: 0;
        }
        .template-select, .quantity-input {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s;
            box-sizing: border-box;
            height: 32px;
            line-height: 1.4;
        }
        .template-select:focus, .quantity-input:focus {
            border-color: #1E9FFF;
            outline: none;
            box-shadow: 0 0 3px rgba(30, 159, 255, 0.3);
        }

        .production-btn {
            width: 100%;
            padding: 6px 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 10px;
            flex-shrink: 0;
        }
        .production-btn:hover:not(:disabled) {
            background: #218838;
        }
        .production-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        /* 整合的信息容器样式 */
        .info-container {
            margin-top: 20px;
            flex-shrink: 0;
        }
        .info-row {
            display: flex;
            gap: 15px;
            align-items: stretch; /* 改为stretch确保高度一致 */
        }
        .template-info-compact {
            flex: 1;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            min-height: 250px; /* 增加最小高度 */
            display: flex;
            flex-direction: column;
        }
        .calculation-result-compact {
            flex: 1;
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 12px;
            min-height: 250px; /* 与左侧保持一致 */
            max-height: 420px; /* 适当增加最大高度 */
            display: flex;
            flex-direction: column;
        }

        /* 计算结果的总成本固定区域 */
        .calc-total-cost-fixed {
            flex-shrink: 0;
            background: #1E9FFF;
            color: white;
            padding: 4px 8px; /* 减小内边距 */
            margin: -12px -12px 8px -12px; /* 减小下边距 */
            border-radius: 6px 6px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            font-size: 12px; /* 减小字体大小 */
            height: 24px; /* 固定较小的高度 */
        }

        /* 计算结果的滚动内容区域 */
        .calc-details-scroll {
            flex: 1;
            overflow-y: auto;
            padding-right: 4px; /* 为滚动条留出空间 */
        }

        /* 保留原有样式作为备用 */
        .template-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            display: none;
            flex-shrink: 0;
        }
        .template-info.show {
            display: block;
        }
        .template-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .ingredient-list {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .ingredient-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .ingredient-list li:last-child {
            border-bottom: none;
        }
        /* 紧凑版本的标题样式 */
        .template-info-compact h4,
        .calculation-result-compact h4 {
            margin: 0 0 12px 0;
            color: #333;
            font-size: 14px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 6px;
            flex-shrink: 0; /* 标题不压缩 */
            height: 32px; /* 固定标题高度确保对齐 */
            display: flex;
            align-items: center;
        }

        /* 紧凑版本的内容区域 */
        .template-info-compact > div,
        .calculation-result-compact > div {
            flex: 1; /* 内容区域填充剩余空间 */
            overflow-y: auto;
        }

        /* 紧凑版本的配方列表样式 */
        .template-info-compact .ingredient-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .template-info-compact .ingredient-list li {
            padding: 4px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }
        .template-info-compact .ingredient-list li:last-child {
            border-bottom: none;
        }

        /* 紧凑版本的计算结果样式 */
        .calculation-result-compact .calc-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 4px 0;
            border-bottom: 1px solid #cce7ff;
            flex-wrap: wrap;
            font-size: 12px;
            min-height: 20px; /* 确保每行有最小高度 */
        }
        .calculation-result-compact .calc-item.sufficient {
            color: #28a745;
        }
        .calculation-result-compact .calc-item.insufficient {
            color: #dc3545;
        }
        /* 移除total-cost样式，因为现在总成本固定在顶部显示 */
        .calculation-result-compact .calc-item .item-name {
            font-weight: bold;
            margin-bottom: 2px;
            flex: 1;
        }
        .calculation-result-compact .calc-item .item-detail {
            font-size: 11px;
            color: #666;
            flex: 1;
            margin-right: 8px;
        }
        .calculation-result-compact .calc-item .status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            background: #f8f9fa;
            white-space: nowrap;
        }
        .calculation-result-compact .calc-item.sufficient .status {
            background: #d4edda;
            color: #155724;
        }
        .calculation-result-compact .calc-item.insufficient .status {
            background: #f8d7da;
            color: #721c24;
        }

        /* 保留原有样式作为备用 */
        .calculation-result {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            display: none;
            flex-shrink: 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .calculation-result.show {
            display: block;
        }
        .calc-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            flex-wrap: wrap;
        }
        .calc-item.sufficient {
            color: #28a745;
        }
        .calc-item.insufficient {
            color: #dc3545;
        }
        .calc-item.total-cost {
            font-weight: bold;
            color: #333;
            border-bottom: none;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 2px solid #1E9FFF;
            align-items: center;
        }
        .calc-item .item-name {
            font-weight: bold;
            margin-bottom: 4px;
            flex: 1;
        }
        .calc-item .item-detail {
            font-size: 12px;
            color: #666;
            flex: 1;
            margin-right: 10px;
        }
        .calc-item .status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 3px;
            background: #f8f9fa;
            white-space: nowrap;
        }
        .calc-item.sufficient .status {
            background: #d4edda;
            color: #155724;
        }
        .calc-item.insufficient .status {
            background: #f8d7da;
            color: #721c24;
        }
        .calculation-result h4 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .calc-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #cce7ff;
        }
        .calc-item:last-child {
            border-bottom: none;
            font-weight: bold;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 2px solid #0066cc;
        }

        .plan-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 2px solid #1E9FFF;
            padding-bottom: 8px;
        }

        /* 新的布局区域样式 */
        .plan-header {
            padding: 20px 20px 0 20px;
            flex-shrink: 0;
        }

        .plan-header h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 2px solid #1E9FFF;
            padding-bottom: 8px;
        }

        .plan-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px;
            margin: 15px 0;
        }

        .plan-footer {
            padding: 15px 20px 20px 20px;
            text-align: center;
            flex-shrink: 0;
            border-top: 1px solid #f0f0f0;
        }
        .plan-item, .record-item {
            padding: 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        .plan-item .template-name, .record-item .template-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .plan-item .quantity, .record-item .quantity {
            color: #28a745;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .plan-item .time, .record-item .time {
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;
        }
        .record-item .operator {
            font-size: 12px;
            color: #666;
        }
        .record-actions {
            margin-top: 8px;
            text-align: right;
        }
        .record-actions button {
            font-size: 12px;
            padding: 2px 8px;
        }
        .confirmed-status {
            font-size: 12px;
            color: #28a745;
            font-weight: bold;
        }
        .confirmed-status::before {
            content: "✓ ";
            color: #28a745;
        }

        /* 生产记录弹窗样式 */
        .history-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .history-modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 1000px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }
        .history-modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .history-modal-title {
            font-size: 18px;
            color: #333;
            margin: 0;
        }
        .history-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .history-modal-close:hover {
            color: #666;
        }
        .history-modal-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        .history-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .filter-group label {
            font-size: 14px;
            color: #666;
            margin: 0;
        }
        .filter-input {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .filter-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .filter-btn:hover {
            background: #0d7dd4;
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .history-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .history-table tr:hover {
            background: #f8f9fa;
        }
        .history-table .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .history-table .status.success {
            background: #d4edda;
            color: #155724;
        }
        .history-table .status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        .detail-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .detail-btn:hover {
            background: #0d7dd4;
        }

        /* 响应式设计 - 优化布局结构 */
        @media (max-width: 1200px) {
            .production-main {
                flex-direction: column;
                height: auto;
            }
            .production-plan {
                width: 100%;
                margin-top: 20px;
                height: 400px;
            }
        }

        @media (max-width: 768px) {
            .production-container {
                padding: 10px;
            }
            .production-plan {
                height: 300px;
            }
            .plan-header h3 {
                font-size: 16px;
            }
            .plan-header {
                padding: 15px 15px 0 15px;
            }
            .plan-content {
                padding: 0 15px;
            }
            .plan-footer {
                padding: 10px 15px 15px 15px;
            }

            /* 移动端紧凑布局调整 */
            .info-row {
                flex-direction: column;
                gap: 10px;
                align-items: stretch; /* 保持拉伸对齐 */
            }
            .template-info-compact,
            .calculation-result-compact {
                min-height: 180px; /* 移动端适当高度 */
                max-height: none; /* 移动端取消最大高度限制 */
            }
            .template-info-compact h4,
            .calculation-result-compact h4 {
                font-size: 13px;
                height: 28px; /* 移动端调整标题高度 */
            }
        }

        /* 拆零验证弹窗样式 */
        .split-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .split-modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 防止内容溢出 */
        }
        .split-modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .split-modal-title {
            font-size: 18px;
            color: #333;
            margin: 0;
        }
        .split-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .split-modal-close:hover {
            color: #666;
        }
        .split-modal-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
            box-sizing: border-box; /* 确保padding不会导致溢出 */
            min-height: 0; /* 允许flex子项收缩 */
        }
        .split-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .split-info p {
            margin: 5px 0;
            color: #333;
        }
        .split-requirements {
            margin-bottom: 20px;
        }
        .split-item {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
            transition: all 0.3s;
        }
        .split-item.verified {
            border-color: #28a745;
            background: #f8fff9;
        }
        .split-item.over-quantity {
            border-color: #ff4444;
            background: #fff8f8;
        }
        .split-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .item-name {
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }
        .item-status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            background: #f8f9fa;
            color: #666;
        }
        .split-item-detail {
            margin-bottom: 15px;
        }
        .split-item-detail p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .split-item-scan {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .scan-input {
            flex: 2;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .bottles-input {
            width: 80px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
        }
        .split-actions {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        .split-actions button {
            margin: 0 10px;
        }

        /* 扫码区域样式 */
        .scanner-section {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            box-sizing: border-box; /* 确保padding不会导致溢出 */
        }
        .scanner-section .form-group {
            margin-bottom: 10px;
        }
        .scanner-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .scanner-input {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #1E9FFF;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            transition: border-color 0.3s;
            box-sizing: border-box; /* 确保padding不会导致溢出 */
            max-width: 100%; /* 防止超出父容器 */
        }
        .scanner-input:focus {
            outline: none;
            border-color: #0066cc;
            box-shadow: 0 0 5px rgba(30, 159, 255, 0.3);
        }
        .scanner-tips {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 12px;
            flex-wrap: wrap; /* 允许换行 */
        }
        .scanner-tips i {
            margin-right: 5px;
            color: #1E9FFF;
            flex-shrink: 0; /* 图标不压缩 */
        }
    </style>
</head>
<body>
<div class="production-container">
    <!-- 顶部标题栏 -->
    <div class="production-header">
        <h1 class="production-title">生产计划</h1>
        <div class="header-buttons">
            <button class="back-btn" id="productionHistoryBtn">
                <i class="layui-icon layui-icon-template-1"></i>
                生产记录
            </button>
            <a href="{:url('shop/Production/index')}" class="back-btn">
                <i class="layui-icon layui-icon-return"></i>
                返回生产端
            </a>
        </div>
    </div>

    <!-- 主要内容区 -->
    <div class="production-main">
        <!-- 左侧表单区 -->
        <div class="production-form">
            <div class="form-row">
                <div class="form-group form-group-half">
                    <label>选择处方模板：</label>
                    <select class="template-select" id="templateSelect">
                        <option value="">请选择处方模板</option>
                    </select>
                </div>

                <div class="form-group form-group-half">
                    <label>生产数量：</label>
                    <input type="number" class="quantity-input" id="productionQuantity" placeholder="请输入生产套数" min="1">
                </div>
            </div>
            
            <button class="production-btn" id="startProductionBtn" disabled>开始生产</button>

            <!-- 模板信息和计算结果整合显示 -->
            <div class="info-container" id="infoContainer" style="display: none;">
                <div class="info-row">
                    <!-- 模板信息显示 -->
                    <div class="template-info-compact" id="templateInfo">
                        <h4 id="templateName">模板名称</h4>
                        <div>
                            <strong>配方组成：</strong>
                            <ul class="ingredient-list" id="ingredientList"></ul>
                        </div>
                    </div>

                    <!-- 计算结果显示 -->
                    <div class="calculation-result-compact" id="calculationResult">
                        <h4>生产计算结果</h4>
                        <!-- 固定显示的总成本 -->
                        <div class="calc-total-cost-fixed" id="totalCostFixed" style="flex: unset">
                            <span>预计总成本</span>
                            <span id="totalCostAmount">￥0.00</span>
                        </div>
                        <!-- 可滚动的详细内容 -->
                        <div class="calc-details-scroll" id="calcDetails"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧记录区 -->
        <div class="production-plan">
            <!-- 标题区域 -->
            <div class="plan-header">
                <h3>今日生产记录</h3>
            </div>

            <!-- 滚动内容区域 -->
            <div class="plan-content">
                <div id="todayRecords">
                    <div class="record-item">
                        <div class="template-name">暂无生产记录</div>
                        <div class="quantity">完成生产后将显示记录</div>
                    </div>
                </div>
            </div>

            <!-- 固定按钮区域 -->
            <div class="plan-footer">
                <button class="layui-btn layui-btn-sm" onclick="loadTodayRecords()">刷新记录</button>
            </div>
        </div>
    </div>
</div>

<script src="__STATIC__/js/jquery-3.1.1.js"></script>
<script src="__STATIC__/ext/layui/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 全局变量
    var templates = [];
    var splitRequirements = []; // 存储拆零需求数据
    var lastInputTime = 0; // 上次输入时间
    var scannerTimer = null; // 扫描枪定时器
    var isProcessingScan = false; // 防止重复处理扫码

    // 初始化
    $(document).ready(function() {
        loadTemplates();
        loadTodayRecords();
        bindEvents();
    });
    
    // 绑定事件
    function bindEvents() {
        // 模板选择事件
        $('#templateSelect').on('change', function() {
            var templateId = $(this).val();
            if (templateId) {
                showTemplateInfo(templateId);
                calculateProduction();
            } else {
                hideTemplateInfo();
                hideCalculationResult();
            }
            updateProductionBtn();
        });
        
        // 数量输入事件
        $('#productionQuantity').on('input', function() {
            calculateProduction();
            updateProductionBtn();
        });
        
        // 开始生产按钮
        $('#startProductionBtn').on('click', function() {
            startProduction();
        });

        // 生产记录按钮
        $('#productionHistoryBtn').on('click', function() {
            showProductionHistory();
        });
    }
    
    // 加载模板列表
    function loadTemplates() {
        $.ajax({
            url: '{:url("shop/PharmacyManagement/getTemplateList")}',
            type: 'GET',
            success: function(res) {
                if (res.code === 0) {  // 修复：使用正确的成功代码
                    templates = res.data;
                    renderTemplateOptions();
                } else {
                    layer.msg(res.message || '加载模板失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('加载模板失败', {icon: 2});
            }
        });
    }
    
    // 渲染模板选项
    function renderTemplateOptions() {
        var html = '<option value="">请选择处方模板</option>';
        templates.forEach(function(template) {
            html += '<option value="' + template.class_id + '">' + template.class_name + '</option>';
        });
        $('#templateSelect').html(html);
    }
    
    // 显示模板信息（紧凑版本）
    function showTemplateInfo(templateId) {
        var template = templates.find(t => t.class_id == templateId);
        if (template) {
            $('#templateName').text(template.class_name);

            var ingredientHtml = '';

            // 解析商品名称和数量
            try {
                var goodsNames = JSON.parse(template.goods_name || '[]');
                var goodsNums = JSON.parse(template.goods_num || '[]');

                if (Array.isArray(goodsNames) && Array.isArray(goodsNums)) {
                    for (var i = 0; i < goodsNames.length && i < goodsNums.length; i++) {
                        if (goodsNames[i] && goodsNums[i]) {
                            ingredientHtml += '<li><span>' + goodsNames[i] + '</span><span>' + goodsNums[i] + '颗</span></li>';
                        }
                    }
                }
            } catch (e) {
                console.warn('解析模板配方数据失败:', e);
                ingredientHtml = '<li>配方数据解析失败</li>';
            }

            if (!ingredientHtml) {
                ingredientHtml = '<li>暂无配方信息</li>';
            }

            $('#ingredientList').html(ingredientHtml);
            $('#infoContainer').show(); // 显示整合容器
        }
    }

    // 隐藏模板信息（紧凑版本）
    function hideTemplateInfo() {
        $('#infoContainer').hide(); // 隐藏整合容器
    }
    
    // 计算生产需求
    function calculateProduction() {
        var templateId = $('#templateSelect').val();
        var quantity = parseInt($('#productionQuantity').val()) || 0;
        
        if (!templateId || quantity <= 0) {
            hideCalculationResult();
            return;
        }
        
        var template = templates.find(t => t.class_id == templateId);
        if (!template) return;
        
        $.ajax({
            url: '{:url("shop/PharmacyManagement/calculateProduction")}',
            type: 'POST',
            data: {
                template_id: templateId,
                quantity: quantity
            },
            success: function(res) {
                if (res.code === 0) {  // 修复：使用正确的成功代码
                    showCalculationResult(res.data);
                } else {
                    layer.msg(res.message || '计算失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('计算失败', {icon: 2});
            }
        });
    }
    
    // 显示计算结果（紧凑版本）
    function showCalculationResult(data) {
        var detailsHtml = '';
        var totalCost = 0;

        // 检查数据结构并安全处理
        if (data && data.ingredients && Array.isArray(data.ingredients)) {
            data.ingredients.forEach(function(item) {
                var statusClass = item.sufficient ? 'sufficient' : 'insufficient';
                var statusText = item.sufficient ? '库存充足' : '库存不足';

                // 显示详细的库存信息（强调整瓶库存使用）
                var stockDetail = '';
                var bottleInfo = '';

                if (item.bottles_needed && item.bottles_available !== undefined) {
                    // 新版本：显示整瓶需求和可用情况
                    bottleInfo = '需要' + item.bottles_needed + '瓶 | 可用' + item.bottles_available + '瓶';
                    stockDetail = '(整瓶:' + item.whole_stock + ', 散药:' + item.loose_stock + ', 每瓶:' + item.daizhuang + '颗)';
                } else {
                    // 兼容旧版本
                    stockDetail = '(整瓶:' + item.whole_stock + ', 散药:' + item.loose_stock + ', 每瓶:' + item.daizhuang + '颗)';
                    bottleInfo = '需要' + item.need_quantity + '颗 | 可用:' + item.available_stock + '颗';
                }

                detailsHtml += '<div class="calc-item ' + statusClass + '">' +
                              '<div class="item-name">' + item.sku_name + '</div>' +
                              '<div class="item-detail">' + bottleInfo + ' ' + stockDetail + '</div>' +
                              '<span class="status">' + statusText + '</span>' +
                              '</div>';
            });
        }

        // 处理总成本
        if (data && typeof data.total_cost !== 'undefined') {
            totalCost = parseFloat(data.total_cost);
        }

        // 如果没有详细内容，显示提示信息
        if (!detailsHtml) {
            detailsHtml = '<div class="calc-item">暂无计算结果</div>';
        }

        // 更新固定的总成本显示
        $('#totalCostAmount').text('￥' + totalCost.toFixed(2));

        // 更新滚动区域的详细内容
        $('#calcDetails').html(detailsHtml);

        // 显示整合容器
        $('#infoContainer').show();
    }

    // 隐藏计算结果（紧凑版本）
    function hideCalculationResult() {
        $('#totalCostAmount').text('￥0.00'); // 重置总成本显示
        $('#calcDetails').html(''); // 清空详细内容
        $('#infoContainer').hide(); // 隐藏整合容器
    }
    
    // 更新生产按钮状态
    function updateProductionBtn() {
        var templateId = $('#templateSelect').val();
        var quantity = parseInt($('#productionQuantity').val()) || 0;
        var canProduce = templateId && quantity > 0;

        $('#startProductionBtn').prop('disabled', !canProduce);
    }
    
    // 开始生产（增强版 - 支持拆零步骤）
    function startProduction() {
        var templateId = $('#templateSelect').val();
        var quantity = parseInt($('#productionQuantity').val()) || 0;

        if (!templateId || quantity <= 0) {
            layer.msg('请选择模板和输入生产数量', {icon: 2});
            return;
        }

        // 第一步：获取拆零需求
        var loadingIndex = layer.load(2);

        $.ajax({
            url: '{:url("shop/PharmacyManagement/packageProduction")}',
            type: 'POST',
            data: {
                template_id: templateId,
                quantity: quantity,
                split_verified: 0 // 未完成拆零验证
            },
            success: function(res) {
                layer.close(loadingIndex);

                if (res.code === 2) {
                    // 需要拆零验证，显示拆零弹窗
                    showSplitModal(res.data, templateId, quantity);
                } else if (res.code === 0) {
                    // 直接生产成功（可能是配置为跳过拆零步骤）
                    layer.msg('生产完成！', {icon: 1});
                    loadTodayRecords();
                    resetForm();
                } else {
                    layer.msg(res.message || '获取拆零需求失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('获取拆零需求失败', {icon: 2});
            }
        });
    }

    // 显示拆零验证弹窗
    function showSplitModal(splitData, templateId, quantity) {
        // 保存拆零需求数据到全局变量
        splitRequirements = splitData.requirements;

        var modalHtml = `
            <div class="split-modal" id="splitModal">
                <div class="split-modal-content">
                    <div class="split-modal-header">
                        <h2 class="split-modal-title">拆零验证 - ${splitData.template_name}</h2>
                        <button type="button" class="split-modal-close" onclick="closeSplitModal()">&times;</button>
                    </div>
                    <div class="split-modal-body">
                        <div class="split-info">
                            <p><strong>生产数量：</strong>${splitData.production_quantity} 套</p>
                            <p><strong>需要验证的原料：</strong></p>
                        </div>

                        <!-- 扫码枪输入区域 -->
                        <div class="scanner-section">
                            <div class="form-group">
                                <label>扫描商品条码：</label>
                                <input type="text" class="scanner-input" id="splitScannerInput"
                                       placeholder="请扫描商品条码，系统将自动匹配到对应商品" autofocus>
                            </div>
                            <div class="scanner-tips">
                                <i class="layui-icon layui-icon-tips"></i>
                                <span>扫描后自动匹配到对应商品，或手动点击各商品的验证按钮</span>
                            </div>
                        </div>

                        <div class="split-requirements" id="splitRequirements">
                            ${renderSplitRequirements(splitData.requirements)}
                        </div>
                        <div class="split-actions">
                            <button class="layui-btn layui-btn-primary" id="cancelSplitBtn">取消</button>
                            <button class="layui-btn layui-btn-normal layui-btn-disabled" id="confirmSplitBtn" data-template-id="${templateId}" data-quantity="${quantity}" disabled>确认并开始生产 (0/${splitData.requirements.length})</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // 绑定弹窗事件
        bindModalEvents();

        // 绑定扫码枪事件
        bindScannerEvents();

        // 设置焦点到扫码输入框
        setTimeout(function() {
            $('#splitScannerInput').focus();
        }, 100);
    }

    // 渲染拆零需求列表
    function renderSplitRequirements(requirements) {
        var html = '';
        requirements.forEach(function(item, index) {
            html += `
                <div class="split-item" data-sku-id="${item.sku_id}">
                    <div class="split-item-header">
                        <span class="item-name">${item.sku_name}</span>
                        <span class="item-status" id="status_${item.sku_id}">待扫描(0/${item.bottles_needed})</span>
                    </div>
                    <div class="split-item-detail">
                        <p>需要：${item.needed_grains}颗 (${item.bottles_needed}瓶) | 已扫描：0瓶 (0颗)</p>
                        <p>当前库存：整瓶${item.current_whole_stock}瓶，散药${item.current_loose_stock}颗</p>
                        <p>货架号：<span style="color: #ff4444; font-weight: bold;">${item.shelf_no || '未设置'}</span></p>
                    </div>
                    <div class="split-item-scan">
                        <input type="text" class="scan-input" id="scan_${item.sku_id}"
                               placeholder="扫描记录" readonly>
                        <input type="number" class="bottles-input" id="bottles_${item.sku_id}"
                               value="0" min="0" max="${item.current_whole_stock}" readonly>
                        <button class="layui-btn layui-btn-sm layui-btn-disabled" onclick="manualVerify(${item.sku_id})" disabled>手动验证</button>
                    </div>
                </div>
            `;
        });
        return html;
    }

    // 处理扫描输入的回车事件
    function handleScanKeypress(event, skuId) {
        if (event.keyCode === 13) {
            verifyScan(skuId);
        }
    }

    // 验证扫码结果
    function verifyScan(skuId) {
        var scanCode = $('#scan_' + skuId).val().trim();
        var bottlesCount = parseInt($('#bottles_' + skuId).val()) || 1;

        if (!scanCode) {
            layer.msg('请扫描商品条码', {icon: 2});
            return;
        }

        $.ajax({
            url: '{:url("shop/PharmacyManagement/verifySplitScan")}',
            type: 'POST',
            data: {
                sku_id: skuId,
                scan_code: scanCode,
                bottles_count: bottlesCount
            },
            success: function(res) {
                if (res.code === 0) {
                    // 验证成功
                    $('#status_' + skuId).text('已验证').css('color', '#28a745');
                    $('.split-item[data-sku-id="' + skuId + '"]').addClass('verified');
                    layer.msg('验证成功', {icon: 1});
                } else {
                    layer.msg(res.message || '验证失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('验证失败', {icon: 2});
            }
        });
    }

    // 确认拆零并开始生产
    function confirmSplitAndProduce(templateId, quantity) {
        // 检查是否所有项目都已验证
        var totalItems = $('.split-item').length;
        var verifiedItems = $('.split-item.verified').length;

        if (verifiedItems < totalItems) {
            layer.msg('请完成所有原料的拆零验证，当前进度：' + verifiedItems + '/' + totalItems, {icon: 2});
            return;
        }

        // 收集拆零验证详情并进行最终验证
        var splitDetails = [];
        var hasOverQuantity = false;
        var overQuantityItems = [];
        
        $('.split-item.verified').each(function() {
            var skuId = $(this).data('sku-id');
            var scannedBottles = parseInt($('#bottles_' + skuId).val()) || 0;
            var scanCodes = $('#scan_' + skuId).val();
            
            // 查找对应的需求数据进行验证
            var requiredBottles = 0;
            var skuName = '';
            splitRequirements.forEach(function(item) {
                if (item.sku_id == skuId) {
                    requiredBottles = item.bottles_needed;
                    skuName = item.sku_name;
                }
            });
            
            // 检查是否有超量数据
            if (scannedBottles > requiredBottles) {
                hasOverQuantity = true;
                overQuantityItems.push({
                    name: skuName,
                    scanned: scannedBottles,
                    required: requiredBottles,
                    over: scannedBottles - requiredBottles
                });
            }

            splitDetails.push({
                sku_id: skuId,
                scanned_bottles: scannedBottles,
                scan_codes: scanCodes
            });
        });
        
        // 如果有超量数据，阻止提交
        if (hasOverQuantity) {
            var errorMsg = '<div style="text-align: left; line-height: 1.6;"><p><strong>发现超量扫码数据，无法提交：</strong></p>';
            overQuantityItems.forEach(function(item) {
                errorMsg += `<p>• ${item.name}：已扫码 ${item.scanned} 瓶，需求 ${item.required} 瓶，超出 <span style="color: #ff4444;">${item.over} 瓶</span></p>`;
            });
            errorMsg += '<p style="color: #666; font-size: 12px; margin-top: 10px;">请重新扫码或联系管理员调整。</p></div>';
            
            layer.alert(errorMsg, {
                title: '数据验证失败',
                icon: 2,
                area: ['450px', 'auto'],
                btn: ['知道了'],
                btnAlign: 'c'
            });
            return;
        }

        // 关闭拆零弹窗
        closeSplitModal();

        // 执行正式生产
        var loadingIndex = layer.load(2);

        $.ajax({
            url: '{:url("shop/PharmacyManagement/packageProduction")}',
            type: 'POST',
            data: {
                template_id: templateId,
                quantity: quantity,
                split_verified: 1, // 已完成拆零验证
                split_details: JSON.stringify(splitDetails) // 拆零验证详情
            },
            success: function(res) {
                layer.close(loadingIndex);
                if (res.code === 0) {
                    layer.msg('生产完成！', {icon: 1});
                    loadTodayRecords();
                    resetForm();
                } else {
                    layer.msg(res.message || '生产失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('生产失败', {icon: 2});
            }
        });
    }

    // 绑定弹窗事件
    function bindModalEvents() {
        // 点击弹窗背景关闭
        $('#splitModal').on('click', function(e) {
            if (e.target === this) {
                closeSplitModal();
            }
        });

        // ESC键关闭弹窗
        $(document).on('keydown.splitModal', function(e) {
            if (e.keyCode === 27) { // ESC键
                closeSplitModal();
            }
        });

        // 取消按钮
        $('#cancelSplitBtn').on('click', function() {
            closeSplitModal();
        });

        // 确认并开始生产按钮
        $('#confirmSplitBtn').on('click', function() {
            var templateId = $(this).data('template-id');
            var quantity = $(this).data('quantity');
            confirmSplitAndProduce(templateId, quantity);
        });
    }

    // 绑定扫码枪事件
    function bindScannerEvents() {
        // 扫描输入框事件 - 多重事件监听适配扫描枪
        $('#splitScannerInput').on('keypress keydown keyup input paste', function(e) {
            var currentTime = Date.now();
            var inputValue = $(this).val().trim();

            // 处理回车键
            if (e.type === 'keypress' && e.which === 13) {
                e.preventDefault();
                if (inputValue) {
                    processScannerInput(inputValue);
                }
                return;
            }

            // 处理粘贴事件
            if (e.type === 'paste') {
                var self = this;
                setTimeout(function() {
                    var pastedValue = $(self).val().trim();
                    if (pastedValue) {
                        processScannerInput(pastedValue);
                    }
                }, 10);
                return;
            }

            // 扫描枪输入检测逻辑
            if (e.type === 'input') {
                var timeDiff = currentTime - lastInputTime;
                lastInputTime = currentTime;

                // 清除之前的定时器
                if (scannerTimer) {
                    clearTimeout(scannerTimer);
                }

                // 如果输入速度很快（扫描枪特征），或者检测到完整条码
                if (timeDiff < 50 || inputValue.length >= 8) {
                    // 设置短暂延迟，等待扫描枪输入完成
                    scannerTimer = setTimeout(function() {
                        var finalValue = $('#splitScannerInput').val().trim();
                        if (finalValue && finalValue.length >= 4) { // 最少4位字符
                            processScannerInput(finalValue);
                        }
                    }, 100);
                }
            }
        });
    }

    // 处理扫码输入
    function processScannerInput(scanCode) {
        if (!scanCode) return;

        // 防止重复处理
        if (isProcessingScan) {
            console.log('正在处理扫码，跳过重复请求');
            return;
        }

        isProcessingScan = true;

        // 显示处理中状态
        var $input = $('#splitScannerInput');
        $input.prop('disabled', true).val('处理中...');

        // 获取候选SKU列表
        var candidateSkuList = [];
        splitRequirements.forEach(function(item) {
            // 只包含未验证的商品
            if (!$('.split-item[data-sku-id="' + item.sku_id + '"]').hasClass('verified')) {
                candidateSkuList.push(item.sku_id);
            }
        });

        // 调用后端接口查找匹配的SKU
        $.ajax({
            url: '{:url("shop/PharmacyManagement/findSkuByScanCode")}',
            type: 'POST',
            data: {
                scan_code: scanCode,
                sku_list: candidateSkuList
            },
            success: function(res) {
                // 重置处理状态
                isProcessingScan = false;
                $input.prop('disabled', false).val('').focus();

                if (res.code === 0) {
                    var matchedSku = res.data;

                    // 检查匹配的SKU是否在拆零需求列表中
                    var matchedItem = null;
                    splitRequirements.forEach(function(item) {
                        if (item.sku_id == matchedSku.sku_id) {
                            matchedItem = item;
                        }
                    });

                    if (matchedItem) {
                        // 处理扫码数量叠加
                        var scanResult = processScanQuantity(matchedItem, scanCode);

                        // 只有验证成功时才显示成功提示
                        if (scanResult) {
                            layer.msg('已扫描：' + matchedItem.sku_name, {icon: 1, time: 1500});
                        }
                    } else {
                        layer.msg('扫描的商品不在当前生产需求中', {icon: 2});
                    }
                } else {
                    layer.msg(res.message || '未找到匹配的商品', {icon: 2});
                }
            },
            error: function() {
                // 重置处理状态
                isProcessingScan = false;
                $input.prop('disabled', false).val('').focus();
                layer.msg('查找商品失败，请重试', {icon: 2});
            }
        });
    }

    // 处理扫码数量叠加
    function processScanQuantity(matchedItem, scanCode) {
        var skuId = matchedItem.sku_id;
        var $item = $('.split-item[data-sku-id="' + skuId + '"]');
        var $bottlesInput = $('#bottles_' + skuId);
        var $scanInput = $('#scan_' + skuId);
        var $status = $('#status_' + skuId);

        // 获取当前已扫描数量
        var currentBottles = parseInt($bottlesInput.val()) || 0;
        var needBottles = matchedItem.bottles_needed;

        // 验证扫码数量不能超过需求量
        if (currentBottles >= needBottles) {
            showOverQuantityError(matchedItem.sku_name, currentBottles, needBottles);
            return false; // 返回false表示验证失败
        }

        var scannedBottles = currentBottles + 1; // 每次扫码增加1瓶

        // 更新瓶数
        $bottlesInput.val(scannedBottles);

        // 更新扫码记录（可以记录多个扫码）
        var currentScanCodes = $scanInput.val();
        if (currentScanCodes) {
            $scanInput.val(currentScanCodes + ',' + scanCode);
        } else {
            $scanInput.val(scanCode);
        }

        // 检查是否满足需求
        if (scannedBottles > needBottles) {
            // 超量状态（理论上不应该到达这里，因为前面已经阻止）
            $status.text('超量(' + scannedBottles + '/' + needBottles + ')').css('color', '#ff4444');
            $item.removeClass('verified').addClass('over-quantity');
        } else if (scannedBottles === needBottles) {
            // 刚好满足需求，标记为已验证
            $status.text('已满足(' + scannedBottles + '/' + needBottles + ')').css('color', '#28a745');
            $item.addClass('verified').removeClass('over-quantity');
        } else {
            // 未满足需求，显示进度
            $status.text('进行中(' + scannedBottles + '/' + needBottles + ')').css('color', '#ff9800');
            $item.removeClass('verified').removeClass('over-quantity');
        }

        // 更新详细信息显示
        updateSplitItemDetail(matchedItem, scannedBottles);

        // 检查是否所有商品都已满足需求
        checkAllItemsVerified();
        
        return true; // 返回true表示验证成功
    }

    // 更新拆零项目详细信息
    function updateSplitItemDetail(matchedItem, scannedBottles) {
        var skuId = matchedItem.sku_id;
        var $detail = $('.split-item[data-sku-id="' + skuId + '"] .split-item-detail');

        var needBottles = matchedItem.bottles_needed;
        var needGrains = matchedItem.needed_grains;
        var currentGrains = scannedBottles * matchedItem.grains_per_bottle;

        var detailHtml = `
            <p>需要：${needGrains}颗 (${needBottles}瓶) | 已扫描：${scannedBottles}瓶 (${currentGrains}颗)</p>
            <p>当前库存：整瓶${matchedItem.current_whole_stock}瓶，散药${matchedItem.current_loose_stock}颗</p>
        `;

        $detail.html(detailHtml);
    }

    // 显示超量扫码错误提示
    function showOverQuantityError(skuName, currentBottles, needBottles) {
        var overQuantity = currentBottles - needBottles + 1; // +1 是因为这次扫码会增加1瓶
        var errorMsg = `<div style="text-align: left; line-height: 1.6;">
            <p><strong>扫码数量超限！</strong></p>
            <p><strong>商品：</strong>${skuName}</p>
            <p><strong>已扫码：</strong>${currentBottles} 瓶</p>
            <p><strong>计划需求：</strong>${needBottles} 瓶</p>
            <p><strong>本次扫码将超出：</strong><span style="color: #ff4444;">${overQuantity} 瓶</span></p>
            <p style="color: #666; font-size: 12px; margin-top: 10px;">请检查扫码商品是否正确，或联系管理员调整生产计划。</p>
        </div>`;
        
        layer.alert(errorMsg, {
            title: '扫码验证失败',
            icon: 2,
            area: ['400px', 'auto'],
            btn: ['知道了'],
            btnAlign: 'c'
        });
    }

    // 检查所有商品是否都已验证
    function checkAllItemsVerified() {
        var totalItems = $('.split-item').length;
        var verifiedItems = $('.split-item.verified').length;

        var $confirmBtn = $('#confirmSplitBtn');

        if (verifiedItems >= totalItems) {
            $confirmBtn.removeClass('layui-btn-disabled').prop('disabled', false);
            $confirmBtn.text('确认并开始生产 ✓');

            // 延迟显示完成提示，避免与扫码提示冲突
            setTimeout(function() {
                if (verifiedItems >= totalItems) { // 再次确认状态
                    layer.msg('所有原料已验证完成，可以开始生产！', {icon: 1, time: 2000});
                }
            }, 800);
        } else {
            $confirmBtn.addClass('layui-btn-disabled').prop('disabled', true);
            $confirmBtn.text('确认并开始生产 (' + verifiedItems + '/' + totalItems + ')');
        }
    }

    // 关闭拆零弹窗
    function closeSplitModal() {
        // 清理定时器
        if (scannerTimer) {
            clearTimeout(scannerTimer);
            scannerTimer = null;
        }

        // 重置处理状态
        isProcessingScan = false;

        // 清理事件绑定
        $(document).off('keydown.splitModal');

        // 移除弹窗
        $('#splitModal').remove();
    }

    // 重置表单
    function resetForm() {
        $('#templateSelect').val('');
        $('#productionQuantity').val('');
        hideTemplateInfo();
        hideCalculationResult();
        updateProductionBtn();
    }
    
    // 加载今日生产记录
    function loadTodayRecords() {
        $.ajax({
            url: '{:url("shop/PharmacyManagement/getTodayProductionRecords")}',
            type: 'POST',
            data: {},
            success: function(res) {
                if (res.code === 0) {
                    renderTodayRecords(res.data);
                } else {
                    renderTodayRecords([]);
                }
            },
            error: function() {
                renderTodayRecords([]);
            }
        });
    }

    // 渲染今日生产记录
    function renderTodayRecords(records) {
        var html = '';
        if (!records || records.length === 0) {
            html = '<div class="record-item">' +
                  '<div class="template-name">暂无生产记录</div>' +
                  '<div class="quantity">完成生产后将显示记录</div>' +
                  '</div>';
        } else {
            records.forEach(function(record) {
                // 检查是否已确认最终套数
                var quantityInfo = '';
                var actionButton = '';

                if (record.quantity_confirmed == 1) {
                    // 已确认
                    quantityInfo = '<div class="quantity">计划：' + record.production_quantity + ' 套，实际：' +
                                  (record.final_quantity || record.production_quantity) + ' 套</div>';
                    actionButton = '<div class="record-actions">' +
                                  '<button class="layui-btn layui-btn-xs layui-btn-normal print-btn" data-production-id="' + record.id + '" style="margin-right: 5px;">打印</button>' +
                                  '<span class="confirmed-status">已确认最终套数</span>' +
                                  '</div>';
                } else {
                    // 未确认
                    quantityInfo = '<div class="quantity">生产数量：' + record.production_quantity + ' 套</div>';
                    actionButton = '<div class="record-actions">' +
                                  '<button class="layui-btn layui-btn-xs layui-btn-normal print-btn" data-production-id="' + record.id + '" style="margin-right: 5px;">打印</button>' +
                                  '<button class="layui-btn layui-btn-xs layui-btn-warm confirm-quantity-btn" data-production-id="' + record.id + '" data-original-quantity="' + record.production_quantity + '">确认最终套数</button>' +
                                  '</div>';
                }

                html += '<div class="record-item">' +
                       '<div class="template-name">' + record.template_name + '</div>' +
                       quantityInfo +
                       '<div class="time">完成时间：' + record.create_time_format + '</div>' +
                       '<div class="operator">操作员：' + record.operator_name + '</div>' +
                       actionButton +
                       '</div>';
            });
        }
        $('#todayRecords').html(html);

        // 绑定确认最终套数按钮事件
        $('.confirm-quantity-btn').off('click').on('click', function() {
            var productionId = $(this).data('production-id');
            var originalQuantity = $(this).data('original-quantity');
            showConfirmQuantityModal(productionId, originalQuantity);
        });

        // 绑定打印按钮事件
        $('.print-btn').off('click').on('click', function() {
            var productionId = $(this).data('production-id');
            printProduction(productionId);
        });
    }

    // 显示确认最终套数弹窗
    function showConfirmQuantityModal(productionId, originalQuantity) {
        layer.prompt({
            title: '确认最终生产套数',
            formType: 0, // 输入框类型
            value: originalQuantity, // 默认值
            area: ['300px', '150px']
        }, function(value, index) {
            var finalQuantity = parseInt(value);

            if (isNaN(finalQuantity) || finalQuantity < 0) {
                layer.msg('请输入有效的套数', {icon: 2});
                return false;
            }

            // 确认最终套数
            confirmFinalQuantity(productionId, finalQuantity, index);
        });
    }

    // 确认最终套数
    function confirmFinalQuantity(productionId, finalQuantity, layerIndex) {
        $.ajax({
            url: '{:url("shop/PharmacyManagement/confirmFinalQuantity")}',
            type: 'POST',
            data: {
                production_id: productionId,
                final_quantity: finalQuantity
            },
            success: function(res) {
                if (res.code === 0) {
                    layer.close(layerIndex);
                    layer.msg('最终套数确认成功', {icon: 1});

                    // 立即更新当前记录的显示状态，避免等待刷新
                    updateRecordStatus(productionId, finalQuantity);

                    // 延迟刷新今日记录，确保数据同步
                    setTimeout(function() {
                        loadTodayRecords();
                    }, 500);
                } else {
                    layer.msg(res.message || '确认失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('确认失败', {icon: 2});
            }
        });
    }

    // 立即更新记录状态显示
    function updateRecordStatus(productionId, finalQuantity) {
        // 查找对应的记录项
        $('.confirm-quantity-btn[data-production-id="' + productionId + '"]').each(function() {
            var $btn = $(this);
            var $recordItem = $btn.closest('.record-item');
            var originalQuantity = $btn.data('original-quantity');

            // 更新数量显示
            var $quantityDiv = $recordItem.find('.quantity');
            $quantityDiv.html('计划：' + originalQuantity + ' 套，实际：' + finalQuantity + ' 套');

            // 移除确认按钮，显示已确认状态，保留打印按钮
            var $actionsDiv = $recordItem.find('.record-actions');
            $actionsDiv.html('<button class="layui-btn layui-btn-xs layui-btn-normal print-btn" data-production-id="' + productionId + '" style="margin-right: 5px;">打印</button>' +
                           '<span class="confirmed-status">✓ 已确认最终套数</span>');
            
            // 重新绑定打印按钮事件
            $actionsDiv.find('.print-btn').off('click').on('click', function() {
                var productionId = $(this).data('production-id');
                printProduction(productionId);
            });
        });
    }

    // 显示生产记录弹窗
    function showProductionHistory() {
        const modalHtml = `
            <div class="history-modal" id="historyModal">
                <div class="history-modal-content">
                    <div class="history-modal-header">
                        <h2 class="history-modal-title">生产记录</h2>
                        <button class="history-modal-close" onclick="closeProductionHistory()">&times;</button>
                    </div>
                    <div class="history-modal-body">
                        <div class="history-filters">
                            <div class="filter-group">
                                <label>日期范围：</label>
                                <input type="date" class="filter-input" id="startDate">
                                <span>至</span>
                                <input type="date" class="filter-input" id="endDate">
                            </div>
                            <div class="filter-group">
                                <label>模板名称：</label>
                                <input type="text" class="filter-input" id="templateName" placeholder="输入模板名称">
                            </div>
                            <div class="filter-group">
                                <button class="filter-btn" onclick="searchHistory()">查询</button>
                            </div>
                        </div>
                        <div id="historyTableContainer">
                            <div style="text-align: center; padding: 40px; color: #999;">
                                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // 设置默认日期范围（当天开始）
        const today = new Date();
        $('#endDate').val(today.toISOString().split('T')[0]);
        $('#startDate').val(today.toISOString().split('T')[0]);

        // 自动加载数据
        loadHistoryData();
    }

    // 关闭生产记录弹窗
    window.closeProductionHistory = function() {
        $('#historyModal').remove();
    };

    // 加载历史数据
    function loadHistoryData() {
        const startDate = $('#startDate').val();
        const endDate = $('#endDate').val();
        const templateName = $('#templateName').val().trim();

        $.ajax({
            url: '{:url("shop/PharmacyManagement/getProductionLog")}',
            type: 'POST',
            data: {
                start_date: startDate,
                end_date: endDate,
                template_name: templateName,
                page: 1,
                page_size: 20
            },
            success: function(res) {
                if (res.code === 0) {
                    renderHistoryTable(res);
                } else {
                    $('#historyTableContainer').html('<div style="text-align: center; color: #999; padding: 40px;"><i class="layui-icon layui-icon-face-cry"></i><p>加载失败: ' + (res.msg || '未知错误') + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                $('#historyTableContainer').html('<div style="text-align: center; color: #999; padding: 40px;"><i class="layui-icon layui-icon-face-cry"></i><p>网络错误，请稍后重试</p></div>');
            }
        });
    }

    // 渲染历史记录表格
    function renderHistoryTable(data) {
        let tableHtml = `
            <table class="history-table">
                <thead>
                    <tr>
                        <th>生产时间</th>
                        <th>模板名称</th>
                        <th>生产数量</th>
                        <th>操作员</th>
                        <th>总成本</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>`;

        if (data.data && data.data.length > 0) {
            data.data.forEach(function(item) {
                const statusClass = item.status === 1 ? 'success' : 'failed';
                const statusText = item.status_name || '未知';

                tableHtml += `
                    <tr>
                        <td>${item.create_time_format}</td>
                        <td>${item.template_name}</td>
                        <td>${item.production_quantity}套</td>
                        <td>${item.operator_name || '系统'}</td>
                        <td>¥${parseFloat(item.total_cost || 0).toFixed(2)}</td>
                        <td><span class="status ${statusClass}">${statusText}</span></td>
                        <td>
                            <button class="detail-btn" onclick="viewProductionDetail(${item.id})">详情</button>
                            <button class="detail-btn" onclick="printProduction(${item.id})" style="margin-left: 5px; background: #28a745;">打印</button>
                        </td>
                    </tr>`;
            });
        } else {
            tableHtml += '<tr><td colspan="7" style="text-align: center; color: #999;">暂无记录</td></tr>';
        }

        tableHtml += '</tbody></table>';

        $('#historyTableContainer').html(tableHtml);
    }

    // 搜索历史记录
    window.searchHistory = function() {
        loadHistoryData();
    };

    // 查看生产详情
    window.viewProductionDetail = function(productionId) {
        $.ajax({
            url: '{:url("shop/PharmacyManagement/getProductionDetail")}',
            type: 'POST',
            data: { production_id: productionId },
            success: function(res) {
                if (res.code === 0) {
                    showProductionDetailModal(res.data);
                } else {
                    layer.msg(res.message || '获取详情失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请稍后重试', {icon: 2});
            }
        });
    };

    // 打印生产记录
    window.printProduction = function(productionId) {
        // 打开新的打印页面
        var printUrl = '{:url("shop/PharmacyManagement/printProduction")}?production_id=' + productionId;
        window.open(printUrl, '_blank');
    };

    // 显示生产详情弹窗
    function showProductionDetailModal(productionData) {
        let detailHtml = `
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #1E9FFF;">
                    <h3 style="margin: 0 0 15px 0; color: #333;">生产记录详情</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <p style="margin: 0; padding: 8px 0; color: #666;"><strong>模板名称：</strong>${productionData.template_name}</p>
                        <p style="margin: 0; padding: 8px 0; color: #666;"><strong>生产时间：</strong>${productionData.create_time_format}</p>
                        <p style="margin: 0; padding: 8px 0; color: #666;"><strong>生产数量：</strong>${productionData.production_quantity}套</p>
                        <p style="margin: 0; padding: 8px 0; color: #666;"><strong>操作员：</strong>${productionData.operator_name}</p>
                        <p style="margin: 0; padding: 8px 0; color: #666;"><strong>总成本：</strong>¥${parseFloat(productionData.total_cost || 0).toFixed(2)}</p>
                        <p style="margin: 0; padding: 8px 0; color: #666;"><strong>状态：</strong>${productionData.status_name}</p>
                    </div>
                </div>
                <div>
                    <h4 style="margin: 0 0 15px 0; color: #333;">消耗明细</h4>
                    <div style="max-height: 300px; overflow-y: auto;">
                        ${productionData.consumed_details_text || '暂无明细'}
                    </div>
                </div>
            </div>`;

        layer.open({
            type: 1,
            title: '生产记录详情',
            area: ['800px', '600px'],
            content: detailHtml,
            btn: ['关闭'],
            yes: function(index) {
                layer.close(index);
            }
        });
    }
});
</script>
</body>
</html>
